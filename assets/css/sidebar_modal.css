/* Sidebar Modal Layout Styles */

/* Modal sizing for sidebar layout */
.send-message-modal-sidebar,
.scheduled-message-modal-sidebar {
  max-width: 90vw !important;
  width: 90vw !important;
}

/* Subscription modal sizing to prevent horizontal scrolling */
.modal-box {
  max-width: min(32rem, 90vw) !important;
  width: 100% !important;
  overflow-x: hidden !important;
}

/* Sidebar layout specific styles */
.sidebar-layout {
  display: flex;
  gap: 1.5rem;
  height: 100%;
}

.sidebar-main-content {
  flex: 1;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.sidebar-template-helper {
  width: 40%;
  border-left: 1px solid;
  padding-left: 1.5rem;
  overflow-y: auto;
}

/* Template helper sidebar styles */
.template-helper-sidebar {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.template-helper-header {
  flex-shrink: 0;
  margin-bottom: 1rem;
}

.template-helper-content {
  flex: 1;
  overflow-y: auto;
}

.template-helper-preview {
  flex-shrink: 0;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid;
}

/* Function button grid */
.function-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.function-btn {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  transition: all 0.15s ease-in-out;
}

.function-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Advanced groups accordion */
.advanced-group {
  margin-bottom: 0.5rem;
}

.advanced-group .collapse-title {
  padding: 0.5rem;
  font-size: 0.875rem;
}

.advanced-group .collapse-content {
  padding: 0.5rem;
}

/* Preview area */
.preview-area {
  max-height: 8rem;
  overflow-y: auto;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.75rem;
  line-height: 1.4;
}

/* Submit button at bottom */
.modal-submit-button {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid;
  flex-shrink: 0;
}

/* Responsive design */
@media (max-width: 1024px) {
  .send-message-modal-sidebar,
  .scheduled-message-modal-sidebar {
    max-width: 95vw !important;
    width: 95vw !important;
  }
  
  .sidebar-layout {
    gap: 1rem;
  }
  
  .sidebar-template-helper {
    width: 45%;
    padding-left: 1rem;
  }
}

@media (max-width: 768px) {
  .send-message-modal-sidebar,
  .scheduled-message-modal-sidebar {
    max-width: 98vw !important;
    width: 98vw !important;
    margin: 0.5rem !important;
    height: calc(100vh - 1rem) !important;
  }
  
  .sidebar-layout {
    flex-direction: column;
    gap: 1rem;
  }
  
  .sidebar-main-content {
    flex: none;
    padding-right: 0;
  }
  
  .sidebar-template-helper {
    width: 100%;
    border-left: none;
    border-top: 1px solid;
    padding-left: 0;
    padding-top: 1rem;
    max-height: 40vh;
  }
  
  .function-grid {
    gap: 0.125rem;
  }
  
  .function-btn {
    font-size: 0.625rem;
    padding: 0.125rem 0.25rem;
  }
}

/* Dark mode support */
[data-theme="dark"] .sidebar-template-helper {
  border-left-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .template-helper-preview {
  border-top-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .modal-submit-button {
  border-top-color: rgba(255, 255, 255, 0.1);
}

@media (max-width: 768px) {
  [data-theme="dark"] .sidebar-template-helper {
    border-left: none;
    border-top-color: rgba(255, 255, 255, 0.1);
  }
}

/* Scrollbar styling for sidebar */
.sidebar-template-helper::-webkit-scrollbar,
.sidebar-main-content::-webkit-scrollbar {
  width: 6px;
}

.sidebar-template-helper::-webkit-scrollbar-track,
.sidebar-main-content::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-template-helper::-webkit-scrollbar-thumb,
.sidebar-main-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

[data-theme="dark"] .sidebar-template-helper::-webkit-scrollbar-thumb,
[data-theme="dark"] .sidebar-main-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}

/* Animation for modal opening */
.send-message-modal-sidebar,
.scheduled-message-modal-sidebar {
  animation: modalSlideIn 0.2s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Focus states for accessibility */
.function-btn:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.template-helper-sidebar select:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Loading state for preview */
.preview-loading {
  opacity: 0.6;
  position: relative;
}

.preview-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid #e5e7eb;
  border-top-color: #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Improved spacing for form elements in sidebar layout */
.sidebar-main-content .form-control {
  margin-bottom: 1rem;
}

.sidebar-main-content .space-y-4 > * + * {
  margin-top: 1rem;
}

/* Better visual separation */
.sidebar-template-helper {
  background: rgba(0, 0, 0, 0.02);
}

[data-theme="dark"] .sidebar-template-helper {
  background: rgba(255, 255, 255, 0.02);
}

/* MQTT 5.0 Properties compact styling */
.sidebar-main-content .collapse-content {
  padding: 0.5rem 0.75rem 0.75rem 0.75rem;
}

/* Compact grid layout */
.sidebar-main-content .collapse-content .grid {
  gap: 0.75rem;
}

.sidebar-main-content .collapse-content .form-control {
  margin-bottom: 0;
}

.sidebar-main-content .collapse-content .label {
  padding: 0.25rem 0;
  min-height: auto;
}

.sidebar-main-content .collapse-content .input {
  font-size: 0.75rem;
  height: 2rem;
}

/* User Properties compact styling */
.sidebar-main-content .user-property-row {
  background: rgba(0, 0, 0, 0.02);
  border-radius: 0.375rem;
  padding: 0.5rem;
}

[data-theme="dark"] .sidebar-main-content .user-property-row {
  background: rgba(255, 255, 255, 0.02);
}

.sidebar-main-content .user-property-empty {
  padding: 0.75rem;
  text-align: center;
  color: rgba(0, 0, 0, 0.5);
}

[data-theme="dark"] .sidebar-main-content .user-property-empty {
  color: rgba(255, 255, 255, 0.5);
}

/* Responsive adjustments for compact layout */
@media (min-width: 1200px) {
  .sidebar-main-content .collapse-content .grid {
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .sidebar-main-content .collapse-content {
    padding: 0.5rem;
  }

  .sidebar-main-content .collapse-content .grid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .sidebar-main-content .user-property-row {
    padding: 0.375rem;
  }

  .sidebar-main-content .collapse-content .input {
    font-size: 0.6875rem;
    height: 1.75rem;
  }
}
